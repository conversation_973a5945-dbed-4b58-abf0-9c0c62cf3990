# 编译和运行指南

## 🚀 快速开始

### 前提条件
- C++11兼容编译器 (GCC 4.8+ 或 Clang 3.3+)
- FFmpeg开发库
- JsonCpp库 (仅多传感器工具需要)
- pkg-config

## 📦 依赖安装

### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install build-essential pkg-config
sudo apt-get install libavformat-dev libavcodec-dev libavutil-dev
sudo apt-get install libjsoncpp-dev  # 多传感器工具需要
```

### CentOS/RHEL
```bash
sudo yum install gcc-c++ pkgconfig
sudo yum install ffmpeg-devel
sudo yum install jsoncpp-devel  # 多传感器工具需要
```

### macOS
```bash
brew install pkg-config ffmpeg jsoncpp
```

## 🔧 编译方法

### 方法1: 使用Makefile（推荐）

```bash
# 在项目根目录 rtsp_tools/ 下执行

# 检查依赖
make check-deps

# 编译所有工具
make all

# 或者分别编译
make original      # 仅编译原始RTSP工具
make multi-sensor  # 仅编译多传感器工具

# 查看编译状态
make version

# 清理编译文件
make clean

# 查看所有可用命令
make help
```

### 方法2: 使用编译脚本

#### 编译原始RTSP工具
```bash
chmod +x scripts/build_original.sh
./scripts/build_original.sh
```

#### 编译多传感器工具
```bash
chmod +x scripts/build_multi_sensor.sh
./scripts/build_multi_sensor.sh
```

### 方法3: 手动编译

#### 编译原始RTSP工具
```bash
# 创建输出目录
mkdir -p build/bin

# 编译RTSP服务器
g++ -std=c++11 -O2 -g -Wall -Wextra \
    -o build/bin/rtsp_tool \
    src/original/rtsp_server.cpp \
    $(pkg-config --cflags --libs libavformat libavcodec libavutil) \
    -lpthread

# 编译时间戳处理工具
g++ -std=c++11 -O2 -g -Wall -Wextra \
    -o build/bin/rtsp_timestamp_proc_tool \
    src/original/rtsp_timestamp_proc_tool.cpp \
    $(pkg-config --cflags --libs libavformat libavcodec libavutil) \
    -lpthread
```

#### 编译多传感器工具
```bash
# 创建输出目录
mkdir -p build/bin

# 编译多传感器工具
g++ -std=c++11 -O2 -g -Wall -Wextra \
    -o build/bin/multi_sensor_tool \
    src/multi_sensor/multi_sensor_main.cpp src/multi_sensor/multi_sensor_server.cpp \
    $(pkg-config --cflags --libs libavformat libavcodec libavutil jsoncpp) \
    -lpthread
```

## 🏃 运行方法

### 原始RTSP工具

#### 基本用法
```bash
./build/bin/rtsp_tool -f <配置文件> -g <组名称> [选项]
```

#### 参数说明
- `-f <配置文件>`: INI格式配置文件路径
- `-g <组名称>`: 要播放的组名称
- `-l`: 循环播放模式
- `-d`: 调试模式（显示详细日志）
- `-t <类型>`: SEI帧类型 (0:默认, 1:大华, 2:海康)
- `-h`: 显示帮助信息

#### 运行示例
```bash
# 播放视频流（循环模式 + 调试模式）
./build/bin/rtsp_tool -f test/configs/rtspconfig.txt -g deqing-001 -l -d

# 播放视频流（单次播放）
./build/bin/rtsp_tool -f test/configs/rtspconfig.txt -g deqing-001

# 指定摄像头类型
./build/bin/rtsp_tool -f test/configs/rtspconfig.txt -g deqing-001 -t 1 -d
```

### 多传感器工具

#### 基本用法
```bash
./build/bin/multi_sensor_tool -f <配置文件> -g <组名称> [选项]
```

#### 参数说明（与原始工具相同）
- `-f <配置文件>`: INI格式配置文件路径
- `-g <组名称>`: 要播放的传感器组名称
- `-l`: 循环播放模式
- `-d`: 调试模式（显示详细日志）
- `-t <类型>`: 视频SEI帧类型
- `-h`: 显示帮助信息

#### 运行示例
```bash
# 播放多传感器数据（JSON传感器）
./build/bin/multi_sensor_tool -f test/configs/test_config.txt -g json-only-test -d

# 向后兼容模式（播放视频）
./build/bin/multi_sensor_tool -f test/configs/rtspconfig.txt -g deqing-001 -l -d

# 混合传感器播放
./build/bin/multi_sensor_tool -f test/configs/test_config.txt -g mixed-test -l -d
```

### 时间戳处理工具

#### 基本用法
```bash
./build/bin/rtsp_timestamp_proc_tool <输入视频文件> [输出时间戳文件]
```

#### 运行示例
```bash
# 从视频文件提取时间戳
./build/bin/rtsp_timestamp_proc_tool input_video.ts output_timestamps.txt

# 输出到标准输出
./build/bin/rtsp_timestamp_proc_tool input_video.ts
```

## 🧪 测试运行

### 使用Makefile测试
```bash
# 测试多传感器工具
make test

# 测试原始RTSP工具
make test-original

# 快速功能测试
make quick-test

# 生成测试数据
make test-data
```

### 手动测试

#### 测试JSON传感器
```bash
# 1. 启动UDP监听器（另一个终端）
./scripts/udp_listener.ps1 -Port 9001  # Windows
# 或
nc -u -l 9001  # Linux/macOS

# 2. 运行多传感器工具
./build/bin/multi_sensor_tool -f test/configs/test_config.txt -g json-only-test -d
```

#### 测试视频播放
```bash
# 如果有视频测试文件
./build/bin/rtsp_tool -f test/configs/rtspconfig.txt -g deqing-001 -d
```

## 📁 配置文件格式

### 原始工具配置 (rtspconfig.txt)
```ini
[组名称]
视频文件1.ts,时间戳文件1.txt,rtsp://目标地址1
视频文件2.ts,时间戳文件2.txt,rtsp://目标地址2
```

### 多传感器配置 (test_config.txt)
```ini
[场景名称]
# 新格式：类型前缀
video:相机.ts,相机.txt,rtsp://*************:8554/stream1
fisheye:鱼眼.ts,鱼眼.txt,rtsp://*************:8554/stream2
radar:雷达.json,udp://*************:9001
lidar:激光雷达.json,udp://*************:9002

# 旧格式：向后兼容
视频1.ts,视频1.txt,rtsp://*************:8554/stream1
```

## 🐛 故障排除

### 编译错误

#### 找不到FFmpeg库
```bash
# 错误信息: fatal error: libavformat/avformat.h: No such file or directory
# 解决方案:
sudo apt-get install libavformat-dev libavcodec-dev libavutil-dev  # Ubuntu
sudo yum install ffmpeg-devel  # CentOS
brew install ffmpeg  # macOS
```

#### 找不到JsonCpp库
```bash
# 错误信息: fatal error: json/json.h: No such file or directory
# 解决方案:
sudo apt-get install libjsoncpp-dev  # Ubuntu
sudo yum install jsoncpp-devel  # CentOS
brew install jsoncpp  # macOS
```

#### pkg-config错误
```bash
# 错误信息: pkg-config: command not found
# 解决方案:
sudo apt-get install pkg-config  # Ubuntu
sudo yum install pkgconfig  # CentOS
brew install pkg-config  # macOS
```

### 运行时错误

#### 配置文件错误
```bash
# 错误信息: Cannot open config file
# 解决方案: 检查文件路径和权限
ls -la test/configs/test_config.txt
```

#### 网络端口错误
```bash
# 错误信息: Failed to bind to UDP port
# 解决方案: 检查端口是否被占用
netstat -un | grep 9001
```

#### 视频文件错误
```bash
# 错误信息: Cannot open input file
# 解决方案: 检查视频文件路径
ls -la test/data/video/
```

## 📊 性能监控

### 查看资源使用
```bash
# 监控CPU和内存使用
top -p $(pgrep multi_sensor_tool)

# 监控网络连接
netstat -an | grep :8554  # RTSP端口
netstat -un | grep :9001  # UDP端口
```

### 调试模式
```bash
# 启用详细日志
./build/bin/multi_sensor_tool -f config.txt -g group -d

# 查看系统日志
journalctl -f  # Linux
```

## 📝 开发提示

### 修改源码后重新编译
```bash
make clean && make all
```

### 添加新的传感器类型
1. 修改 `src/multi_sensor/multi_sensor_server.h`
2. 实现新的传感器类
3. 更新配置解析器
4. 重新编译测试

### 调试编译问题
```bash
# 查看详细编译信息
make VERBOSE=1 all

# 或使用脚本查看详细输出
./scripts/build_multi_sensor.sh
```
