# RTSP工具项目文件结构

## 项目概述

本项目包含两个主要工具：
1. **原始RTSP工具** - 多路视频流同步播放
2. **多传感器工具** - 扩展支持视频、雷达、激光雷达等多种传感器数据同步播放

## 推荐的文件结构

```
rtsp_tools/
├── README.md                          # 项目主说明文档
├── PROJECT_STRUCTURE.md               # 本文档
├── Makefile                           # 主编译文件
│
├── src/                               # 源代码目录
│   ├── original/                      # 原始RTSP工具源码
│   │   ├── rtsp_server.cpp
│   │   ├── rtsp_server.h
│   │   └── rtsp_timestamp_proc_tool.cpp
│   └── multi_sensor/                  # 多传感器工具源码
│       ├── multi_sensor_server.cpp
│       ├── multi_sensor_server.h
│       └── multi_sensor_main.cpp
│
├── include/                           # 公共头文件目录
│   └── common/                        # 通用头文件
│
├── build/                             # 编译输出目录
│   ├── bin/                          # 可执行文件
│   ├── obj/                          # 目标文件
│   └── lib/                          # 库文件
│
├── scripts/                           # 脚本文件目录
│   ├── build_original.sh             # 原始工具编译脚本
│   ├── build_multi_sensor.sh         # 多传感器工具编译脚本
│   ├── generate_test_data.bat        # Windows测试数据生成
│   └── udp_listener.ps1              # UDP监听工具
│
├── test/                              # 测试文件目录
│   ├── configs/                       # 测试配置文件
│   │   ├── rtspconfig.txt            # 原始工具配置
│   │   └── test_config.txt           # 多传感器配置
│   ├── data/                         # 测试数据
│   │   ├── json/                     # JSON测试数据
│   │   │   ├── radar_test.json
│   │   │   └── lidar_test.json
│   │   └── video/                    # 视频测试数据
│   │       ├── *.ts                  # 视频文件
│   │       └── *.txt                 # 时间戳文件
│   └── scripts/                      # 测试脚本
│       └── test_multi_sensor.sh
│
├── docs/                              # 文档目录
│   ├── architecture/                  # 架构设计文档
│   │   ├── ARCHITECTURE_SIMPLE.md
│   │   └── ARCHITECTURE_V2.md
│   ├── user_guides/                   # 用户指南
│   │   ├── README_MULTI_SENSOR.md
│   │   └── WINDOWS_BUILD_GUIDE.md
│   └── development/                   # 开发文档
│       └── IMPLEMENTATION_SUMMARY.md
│
├── examples/                          # 示例文件目录
│   ├── configs/                       # 示例配置
│   └── data/                         # 示例数据
│
└── data/                              # 实际数据目录（运行时）
    ├── videos/                        # 视频文件
    ├── timestamps/                    # 时间戳文件
    └── sensors/                       # 传感器数据
        ├── radar/
        └── lidar/
```

## 文件分类说明

### 源代码文件 (src/)
- **原始工具**: `rtsp_server.cpp`, `rtsp_server.h`, `rtsp_timestamp_proc_tool.cpp`
- **多传感器工具**: `multi_sensor_server.cpp`, `multi_sensor_server.h`, `multi_sensor_main.cpp`

### 编译相关文件 (scripts/)
- `build.sh` - 原始工具编译脚本
- `build_multi_sensor.sh` - 多传感器工具编译脚本
- `Makefile` - 主编译文件

### 测试文件 (test/)
- **配置文件**: `rtspconfig.txt`, `test_config.txt`
- **测试数据**: `*.ts`, `*.txt`, `*.json`
- **测试脚本**: `test_multi_sensor.sh`
- **工具脚本**: `udp_listener.ps1`, `generate_test_data.bat`

### 文档文件 (docs/)
- **架构文档**: `ARCHITECTURE_*.md`
- **用户指南**: `README_MULTI_SENSOR.md`, `WINDOWS_BUILD_GUIDE.md`
- **开发文档**: `IMPLEMENTATION_SUMMARY.md`

### 可执行文件
- `rtsp_tool` - 原始RTSP工具
- `rtsp_timestamp_proc_tool` - 时间戳处理工具
- `multi_sensor_tool` - 多传感器工具（编译后生成）

## 文件移动计划

### 第一步：移动源代码文件
```bash
# 原始工具源码
mv rtsp_server.cpp src/original/
mv rtsp_server.h src/original/
mv rtsp_timestamp_proc_tool.cpp src/original/

# 多传感器工具源码
mv multi_sensor_server.cpp src/multi_sensor/
mv multi_sensor_server.h src/multi_sensor/
mv multi_sensor_main.cpp src/multi_sensor/
```

### 第二步：移动脚本文件
```bash
mv build.sh scripts/build_original.sh
mv build_multi_sensor.sh scripts/
mv udp_listener.ps1 scripts/
mv generate_test_data.bat scripts/
mv test_multi_sensor.sh test/scripts/
```

### 第三步：移动测试文件
```bash
# 配置文件
mv rtspconfig.txt test/configs/
mv test_config.txt test/configs/

# JSON测试数据
mv radar_test.json test/data/json/
mv lidar_test.json test/data/json/

# 视频测试数据
mv v_*.ts test/data/video/
mv v_*.txt test/data/video/
```

### 第四步：移动文档文件
```bash
# 架构文档
mv ARCHITECTURE_SIMPLE.md docs/architecture/
mv ARCHITECTURE_V2.md docs/architecture/

# 用户指南
mv README_MULTI_SENSOR.md docs/user_guides/
mv WINDOWS_BUILD_GUIDE.md docs/user_guides/

# 开发文档
mv IMPLEMENTATION_SUMMARY.md docs/development/
```

### 第五步：移动可执行文件
```bash
mv rtsp_tool build/bin/
mv rtsp_timestamp_proc_tool build/bin/
```

## 更新后的编译方式

### 使用Makefile（推荐）
```bash
make original      # 编译原始工具
make multi-sensor  # 编译多传感器工具
make all          # 编译所有工具
make clean        # 清理编译文件
make test         # 运行测试
```

### 使用脚本
```bash
# 编译原始工具
./scripts/build_original.sh

# 编译多传感器工具
./scripts/build_multi_sensor.sh
```

## 配置文件路径更新

更新后的配置文件路径：
- 原始工具配置: `test/configs/rtspconfig.txt`
- 多传感器配置: `test/configs/test_config.txt`

## 运行方式更新

```bash
# 原始工具
./build/bin/rtsp_tool -f test/configs/rtspconfig.txt -g deqing-001 -l -d

# 多传感器工具
./build/bin/multi_sensor_tool -f test/configs/test_config.txt -g json-only-test -d
```

## 优势

1. **清晰的模块分离**: 原始工具和多传感器工具分开管理
2. **标准化结构**: 符合C++项目的标准目录结构
3. **便于维护**: 文件分类清晰，易于查找和维护
4. **便于扩展**: 为未来功能扩展预留了空间
5. **便于测试**: 测试文件和数据统一管理

## 注意事项

1. 移动文件后需要更新Makefile中的路径
2. 需要更新脚本中的相对路径引用
3. 文档中的路径引用需要相应更新
4. 建议在移动文件前备份整个项目
