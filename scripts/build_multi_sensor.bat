@echo off
REM 编译多传感器工具 - Windows版本

echo === 编译多传感器工具 ===

REM 切换到项目根目录
cd /d "%~dp0\.."

REM 创建输出目录
if not exist "build\bin" mkdir build\bin

echo 检查编译环境...

REM 检查g++编译器
where g++ >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到g++编译器
    echo 请安装MinGW-w64或MSYS2环境
    echo 参考: docs\user_guides\WINDOWS_BUILD_GUIDE.md
    pause
    exit /b 1
)

REM 检查pkg-config
where pkg-config >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到pkg-config
    echo 请在MSYS2环境中安装: pacman -S mingw-w64-x86_64-pkg-config
    pause
    exit /b 1
)

REM 检查FFmpeg库
pkg-config --exists libavformat libavcodec libavutil
if %errorlevel% neq 0 (
    echo 错误: 未找到FFmpeg开发库
    echo 请在MSYS2环境中安装: pacman -S mingw-w64-x86_64-ffmpeg
    pause
    exit /b 1
)

REM 检查JsonCpp库
pkg-config --exists jsoncpp
if %errorlevel% neq 0 (
    echo 错误: 未找到JsonCpp开发库
    echo 请在MSYS2环境中安装: pacman -S mingw-w64-x86_64-jsoncpp
    pause
    exit /b 1
)

echo 获取编译参数...
for /f "delims=" %%i in ('pkg-config --cflags libavformat libavcodec libavutil jsoncpp') do set CFLAGS=%%i
for /f "delims=" %%i in ('pkg-config --libs libavformat libavcodec libavutil jsoncpp') do set LIBS=%%i

echo 编译多传感器工具...
g++ -std=c++11 -O2 -g -Wall -Wextra ^
    -o build\bin\multi_sensor_tool.exe ^
    src\multi_sensor\multi_sensor_main.cpp src\multi_sensor\multi_sensor_server.cpp ^
    %CFLAGS% %LIBS% -lpthread -lws2_32

if %errorlevel% equ 0 (
    echo.
    echo === 编译成功 ===
    echo 可执行文件: build\bin\multi_sensor_tool.exe
    dir build\bin\multi_sensor_tool.exe
    echo.
    echo 使用示例:
    echo   build\bin\multi_sensor_tool.exe -f test\configs\test_config.txt -g json-only-test -d
    echo.
    echo   REM 向后兼容模式
    echo   build\bin\multi_sensor_tool.exe -f test\configs\rtspconfig.txt -g deqing-001 -l
    echo.
    echo 帮助信息: build\bin\multi_sensor_tool.exe -h
) else (
    echo.
    echo === 编译失败 ===
    echo 请检查错误信息并确保所有依赖库已正确安装
    pause
    exit /b 1
)

echo.
pause
