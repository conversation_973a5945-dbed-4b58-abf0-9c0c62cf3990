#!/bin/bash

# Multi-Sensor Tool Build Script
# 基于原有build.sh扩展，支持多传感器功能

echo "=== Multi-Sensor Data Synchronous Playback Tool Build Script ==="
echo "Building multi-sensor tool with FFmpeg and JsonCpp dependencies..."

# 检查必要的依赖
echo "Checking dependencies..."

# 检查pkg-config
if ! command -v pkg-config &> /dev/null; then
    echo "Error: pkg-config not found. Please install pkg-config first."
    exit 1
fi

# 检查FFmpeg库
if ! pkg-config --exists libavformat libavcodec libavutil; then
    echo "Error: FFmpeg development libraries not found."
    echo "Please install FFmpeg development packages:"
    echo "  Ubuntu/Debian: sudo apt-get install libavformat-dev libavcodec-dev libavutil-dev"
    echo "  CentOS/RHEL: sudo yum install ffmpeg-devel"
    echo "  macOS: brew install ffmpeg"
    exit 1
fi

# 检查JsonCpp库
JSONCPP_FOUND=false
if pkg-config --exists jsoncpp; then
    JSONCPP_CFLAGS=$(pkg-config --cflags jsoncpp)
    JSONCPP_LIBS=$(pkg-config --libs jsoncpp)
    JSONCPP_FOUND=true
    echo "Found JsonCpp via pkg-config"
elif [ -f /usr/include/json/json.h ] || [ -f /usr/local/include/json/json.h ]; then
    JSONCPP_CFLAGS="-I/usr/include -I/usr/local/include"
    JSONCPP_LIBS="-ljsoncpp"
    JSONCPP_FOUND=true
    echo "Found JsonCpp in system directories"
else
    echo "Warning: JsonCpp not found. Attempting to install..."
    
    # 尝试自动安装JsonCpp
    if command -v apt-get &> /dev/null; then
        echo "Installing JsonCpp on Ubuntu/Debian..."
        sudo apt-get update && sudo apt-get install -y libjsoncpp-dev
    elif command -v yum &> /dev/null; then
        echo "Installing JsonCpp on CentOS/RHEL..."
        sudo yum install -y jsoncpp-devel
    elif command -v brew &> /dev/null; then
        echo "Installing JsonCpp on macOS..."
        brew install jsoncpp
    else
        echo "Error: Cannot automatically install JsonCpp."
        echo "Please install JsonCpp manually:"
        echo "  Ubuntu/Debian: sudo apt-get install libjsoncpp-dev"
        echo "  CentOS/RHEL: sudo yum install jsoncpp-devel"
        echo "  macOS: brew install jsoncpp"
        exit 1
    fi
    
    # 重新检查
    if pkg-config --exists jsoncpp; then
        JSONCPP_CFLAGS=$(pkg-config --cflags jsoncpp)
        JSONCPP_LIBS=$(pkg-config --libs jsoncpp)
        JSONCPP_FOUND=true
        echo "JsonCpp installed successfully"
    else
        echo "Error: JsonCpp installation failed"
        exit 1
    fi
fi

# 获取FFmpeg编译参数
FFMPEG_CFLAGS=$(pkg-config --cflags libavformat libavcodec libavutil)
FFMPEG_LIBS=$(pkg-config --libs libavformat libavcodec libavutil)

echo "FFmpeg CFLAGS: $FFMPEG_CFLAGS"
echo "FFmpeg LIBS: $FFMPEG_LIBS"
echo "JsonCpp CFLAGS: $JSONCPP_CFLAGS"
echo "JsonCpp LIBS: $JSONCPP_LIBS"

# 设置编译参数
CXX_STANDARD="-std=c++11"
OPTIMIZATION="-O2"
DEBUG_FLAGS="-g"
WARNING_FLAGS="-Wall -Wextra"

# 平台特定的网络库
NETWORK_LIBS=""
if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
    NETWORK_LIBS="-lws2_32"
    echo "Detected Windows platform, adding Winsock2 library"
fi

# 切换到项目根目录
cd "$(dirname "$0")/.."

# 创建输出目录
mkdir -p build/bin

# 编译命令
COMPILE_CMD="g++ $CXX_STANDARD $OPTIMIZATION $DEBUG_FLAGS $WARNING_FLAGS \
    -o build/bin/multi_sensor_tool \
    src/multi_sensor/multi_sensor_main.cpp src/multi_sensor/multi_sensor_server.cpp \
    $FFMPEG_CFLAGS $JSONCPP_CFLAGS \
    $FFMPEG_LIBS $JSONCPP_LIBS $NETWORK_LIBS \
    -lpthread"

echo ""
echo "Compiling multi-sensor tool..."
echo "Command: $COMPILE_CMD"
echo ""

# 执行编译
if eval $COMPILE_CMD; then
    echo ""
    echo "=== Build Successful ==="

    # 显示文件信息
    if [ -f "build/bin/multi_sensor_tool" ]; then
        FILE_SIZE=$(ls -lh build/bin/multi_sensor_tool | awk '{print $5}')
        echo "Executable: build/bin/multi_sensor_tool"
        echo "Size: $FILE_SIZE"
        echo ""
        
        # 显示使用说明
        echo "Usage:"
        echo "  ./build/bin/multi_sensor_tool -f <config_file> -g <group_name> [options]"
        echo ""
        echo "Examples:"
        echo "  # Play multi-sensor scene"
        echo "  ./build/bin/multi_sensor_tool -f test/configs/test_config.txt -g json-only-test -l -d"
        echo ""
        echo "  # Backward compatibility with original tool"
        echo "  ./build/bin/multi_sensor_tool -f test/configs/rtspconfig.txt -g deqing-001 -l"
        echo ""
        echo "For help: ./build/bin/multi_sensor_tool -h"
        
    else
        echo "Warning: Executable not found after compilation"
        exit 1
    fi
    
else
    echo ""
    echo "=== Build Failed ==="
    echo "Please check the error messages above and ensure all dependencies are installed."
    exit 1
fi

echo ""
echo "Build script completed successfully!"
