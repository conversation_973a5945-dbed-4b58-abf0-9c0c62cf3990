@echo off
REM 编译原始RTSP工具 - Windows版本

echo === 编译原始RTSP工具 ===

REM 切换到项目根目录
cd /d "%~dp0\.."

REM 创建输出目录
if not exist "build\bin" mkdir build\bin

echo 检查编译环境...

REM 检查g++编译器
where g++ >nul 2>nul
if errorlevel 1 (
    echo 错误: 未找到g++编译器
    echo 请安装MinGW-w64或MSYS2环境
    echo 参考: docs\user_guides\WINDOWS_BUILD_GUIDE.md
    pause
    exit /b 1
)

REM 检查pkg-config
where pkg-config >nul 2>nul
if errorlevel 1 (
    echo 错误: 未找到pkg-config
    echo 请在MSYS2环境中安装: pacman -S mingw-w64-x86_64-pkg-config
    pause
    exit /b 1
)

REM 检查FFmpeg库
pkg-config --exists libavformat libavcodec libavutil >nul 2>nul
if errorlevel 1 (
    echo 错误: 未找到FFmpeg开发库
    echo 请在MSYS2环境中安装: pacman -S mingw-w64-x86_64-ffmpeg
    pause
    exit /b 1
)

echo 获取编译参数...
REM 简化编译，直接使用库名
set CFLAGS=-I/mingw64/include
set LIBS=-lavformat -lavcodec -lavutil

echo 编译RTSP服务器...
g++ -std=c++11 -O2 -g -Wall -Wextra ^
    -o build\bin\rtsp_tool.exe ^
    src\original\rtsp_server.cpp ^
    %CFLAGS% %LIBS% -lpthread -lws2_32

if errorlevel 1 (
    echo ❌ RTSP服务器编译失败！
    pause
    exit /b 1
) else (
    echo ✅ RTSP服务器编译成功: build\bin\rtsp_tool.exe
    dir build\bin\rtsp_tool.exe
)

echo 编译时间戳处理工具...
g++ -std=c++11 -O2 -g -Wall -Wextra ^
    -o build\bin\rtsp_timestamp_proc_tool.exe ^
    src\original\rtsp_timestamp_proc_tool.cpp ^
    %CFLAGS% %LIBS% -lpthread -lws2_32

if errorlevel 1 (
    echo ❌ 时间戳处理工具编译失败！
    pause
    exit /b 1
) else (
    echo ✅ 时间戳处理工具编译成功: build\bin\rtsp_timestamp_proc_tool.exe
    dir build\bin\rtsp_timestamp_proc_tool.exe
)

echo.
echo === 编译完成 ===
echo 可执行文件位置:
echo   - RTSP工具: build\bin\rtsp_tool.exe
echo   - 时间戳工具: build\bin\rtsp_timestamp_proc_tool.exe
echo.
echo 使用示例:
echo   build\bin\rtsp_tool.exe -f test\configs\rtspconfig.txt -g deqing-001 -l -d
echo.
pause
