#!/bin/bash

# 编译原始RTSP工具
echo "=== 编译原始RTSP工具 ==="

# 切换到项目根目录
cd "$(dirname "$0")/.."

# 创建输出目录
mkdir -p build/bin

echo "检查依赖库..."

# 检查pkg-config
if ! command -v pkg-config &> /dev/null; then
    echo "错误: 未找到pkg-config，请先安装pkg-config"
    exit 1
fi

# 检查FFmpeg库
if ! pkg-config --exists libavformat libavcodec libavutil; then
    echo "错误: 未找到FFmpeg开发库"
    echo "请安装FFmpeg开发包:"
    echo "  Ubuntu/Debian: sudo apt-get install libavformat-dev libavcodec-dev libavutil-dev"
    echo "  CentOS/RHEL: sudo yum install ffmpeg-devel"
    echo "  macOS: brew install ffmpeg"
    exit 1
fi

# 获取FFmpeg的编译参数
CFLAGS=$(pkg-config --cflags libavformat libavcodec libavutil)
LIBS=$(pkg-config --libs libavformat libavcodec libavutil)

echo "编译RTSP服务器..."
# 编译RTSP服务器
g++ -std=c++11 -O2 -g -Wall -Wextra \
    -o build/bin/rtsp_tool \
    src/original/rtsp_server.cpp \
    $CFLAGS $LIBS -lpthread

if [ $? -eq 0 ]; then
    echo "✅ RTSP服务器编译成功: build/bin/rtsp_tool"
    ls -lh build/bin/rtsp_tool
else
    echo "❌ RTSP服务器编译失败！"
    exit 1
fi

echo "编译时间戳处理工具..."
# 编译时间戳处理工具
g++ -std=c++11 -O2 -g -Wall -Wextra \
    -o build/bin/rtsp_timestamp_proc_tool \
    src/original/rtsp_timestamp_proc_tool.cpp \
    $CFLAGS $LIBS -lpthread

if [ $? -eq 0 ]; then
    echo "✅ 时间戳处理工具编译成功: build/bin/rtsp_timestamp_proc_tool"
    ls -lh build/bin/rtsp_timestamp_proc_tool
else
    echo "❌ 时间戳处理工具编译失败！"
    exit 1
fi

echo ""
echo "=== 编译完成 ==="
echo "可执行文件位置:"
echo "  - RTSP工具: build/bin/rtsp_tool"
echo "  - 时间戳工具: build/bin/rtsp_timestamp_proc_tool"
echo ""
echo "使用示例:"
echo "  ./build/bin/rtsp_tool -f test/configs/rtspconfig.txt -g deqing-001 -l -d"