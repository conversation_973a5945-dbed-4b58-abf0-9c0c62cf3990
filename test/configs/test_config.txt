# Multi-Sensor Test Configuration File
# 测试用的多传感器配置文件

[test-scene]
# 视频传感器测试（如果有实际的TS文件）
# video:v_10.5.200.218_1723169047302.ts,v_10.5.200.218_1723169047302.txt,rtsp://localhost:8554/stream1
# fisheye:v_10.5.200.223_1723169047327.ts,v_10.5.200.223_1723169047327.txt,rtsp://localhost:8554/stream2

# JSON传感器测试
radar:test/data/json/radar_test.json,udp://127.0.0.1:9001
lidar:test/data/json/lidar_test.json,udp://127.0.0.1:9002

[json-only-test]
# 仅JSON传感器测试
radar:test/data/json/radar_test.json,udp://127.0.0.1:9001
lidar:test/data/json/lidar_test.json,udp://127.0.0.1:9002

[legacy-test]
# 向后兼容性测试（使用现有的TS文件）
test/data/video/v_10.5.200.218_1723169047302.ts,test/data/video/v_10.5.200.218_1723169047302.txt,rtsp://localhost:8554/legacy1
test/data/video/v_10.5.200.223_1723169047327.ts,test/data/video/v_10.5.200.223_1723169047327.txt,rtsp://localhost:8554/legacy2

[mixed-test]
# 混合传感器测试
video:test/data/video/v_10.5.200.218_1723169047302.ts,test/data/video/v_10.5.200.218_1723169047302.txt,rtsp://localhost:8554/video1
radar:test/data/json/radar_test.json,udp://127.0.0.1:9001
lidar:test/data/json/lidar_test.json,udp://127.0.0.1:9002
